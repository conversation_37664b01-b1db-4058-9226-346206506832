import gradio as gr
import fitz  # PyMuPDF for PDF rendering
import os
import tempfile
import datetime
import asyncio
import re
import uuid
import pymupdf
import time  # Added import for time module
import base64  # Added for image to base64 conversion
import json  # Added import for json module
import sqlite3  # Added for SQLite database
from concurrent.futures import ThreadPoolExecutor
from dotenv import load_dotenv, find_dotenv
from pathlib import Path
import io
from PIL import Image
import cv2
import pytesseract
import numpy as np
from loguru import logger
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage
# from magic_pdf.data.data_reader_writer import FileBasedDataReader
# from magic_pdf.tools.common import do_parse, prepare_env
from openai import OpenAI  # Add this import for the OpenAI class
import requests  # Add this import for the requests module

load_dotenv(find_dotenv())

# Database initialization
def get_database_path(file_name):
    """Get the database path for a specific file"""
    # Create database directory for the file
    db_dir = Path("output") / f"{file_name}_db"
    db_dir.mkdir(parents=True, exist_ok=True)
    return db_dir / "topics.db"

def init_database(file_name):
    """Initialize SQLite database for storing topic information for a specific file"""
    db_path = get_database_path(file_name)
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    # Create topics table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS topics (
            topic_id TEXT PRIMARY KEY,
            file_name TEXT NOT NULL,
            page_number INTEGER NOT NULL,
            topic_text TEXT NOT NULL,
            topic_level INTEGER NOT NULL,
            parent_topic_id TEXT,
            FOREIGN KEY (parent_topic_id) REFERENCES topics (topic_id)
        )
    ''')

    conn.commit()
    conn.close()
    return str(db_path)

def extract_topics_from_markdown(markdown_content, file_name, page_number):
    """Extract topics from markdown content and return structured data"""
    topics = []
    lines = markdown_content.split('\n')

    # Stack to keep track of parent topics at each level
    parent_stack = [None] * 7  # Index 0 unused, 1-6 for levels 1-6

    for line in lines:
        line = line.strip()
        if line.startswith('#'):
            # Count the number of # to determine level
            level = 0
            for char in line:
                if char == '#':
                    level += 1
                else:
                    break

            if level <= 6:  # Valid heading levels (1-6 for markdown)
                # Extract topic text (remove # and whitespace)
                topic_text = line[level:].strip()

                if topic_text:  # Only process non-empty topics
                    topic_id = str(uuid.uuid4()).replace('-', '')[:8]  # 8位短UUID

                    # Determine parent topic ID
                    parent_topic_id = None
                    if level > 1:
                        # Find the most recent parent at level-1
                        for parent_level in range(level - 1, 0, -1):
                            if parent_stack[parent_level] is not None:
                                parent_topic_id = parent_stack[parent_level]
                                break

                    # Update parent stack
                    parent_stack[level] = topic_id
                    # Clear deeper levels
                    for i in range(level + 1, 7):
                        parent_stack[i] = None

                    topics.append({
                        'topic_id': topic_id,
                        'file_name': file_name,
                        'page_number': page_number,
                        'topic_text': topic_text,
                        'topic_level': level,
                        'parent_topic_id': parent_topic_id
                    })

    return topics

def save_topics_to_database(topics, file_name):
    """Save topics to SQLite database for a specific file"""
    if not topics:
        return

    db_path = get_database_path(file_name)
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    try:
        # Clear existing topics for this file and page
        if topics:
            first_topic = topics[0]
            cursor.execute('''
                DELETE FROM topics
                WHERE file_name = ? AND page_number = ?
            ''', (first_topic['file_name'], first_topic['page_number']))

        # Insert new topics
        for topic in topics:
            cursor.execute('''
                INSERT INTO topics (topic_id, file_name, page_number, topic_text, topic_level, parent_topic_id)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                topic['topic_id'],
                topic['file_name'],
                topic['page_number'],
                topic['topic_text'],
                topic['topic_level'],
                topic['parent_topic_id']
            ))

        conn.commit()
        print(f"Saved {len(topics)} topics to database: {db_path}")

    except Exception as e:
        print(f"Error saving topics to database: {e}")
        conn.rollback()
    finally:
        conn.close()

def load_topics_from_database(file_name):
    """Load all topics from database for a specific file"""
    if not file_name:
        return []

    try:
        db_path = get_database_path(file_name)
        if not db_path.exists():
            return []

        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        cursor.execute('''
            SELECT topic_id, file_name, page_number, topic_text, topic_level, parent_topic_id
            FROM topics
            ORDER BY page_number, topic_level
        ''')

        results = cursor.fetchall()
        conn.close()

        return results
    except Exception as e:
        print(f"Error loading topics from database: {e}")
        return []

def update_topic_in_database(file_name, topic_id, topic_level, parent_topic_id):
    """Update a topic in the database"""
    try:
        db_path = get_database_path(file_name)
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE topics
            SET topic_level = ?, parent_topic_id = ?
            WHERE topic_id = ?
        ''', (topic_level, parent_topic_id, topic_id))

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"Error updating topic: {e}")
        return False

def delete_topic_from_database(file_name, topic_id):
    """Delete a topic from the database"""
    try:
        db_path = get_database_path(file_name)
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        cursor.execute('DELETE FROM topics WHERE topic_id = ?', (topic_id,))

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"Error deleting topic: {e}")
        return False

def add_topic_to_database(file_name, page_number, topic_text, topic_level, parent_topic_id=None):
    """Add a new topic to the database"""
    try:
        topic_id = str(uuid.uuid4()).replace('-', '')[:8]  # 8位短UUID
        db_path = get_database_path(file_name)
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO topics (topic_id, file_name, page_number, topic_text, topic_level, parent_topic_id)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (topic_id, file_name, page_number, topic_text, topic_level, parent_topic_id))

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"Error adding topic: {e}")
        return False

def sync_dataframe_to_database(pdf_path, dataframe_data):
    """Sync DataFrame data changes to database"""
    if not pdf_path:
        return "请先上传PDF文件", []

    # Check if dataframe_data is empty or None
    if dataframe_data is None:
        return "数据为空", []

    # Convert to list if it's a pandas DataFrame
    if hasattr(dataframe_data, 'values'):
        dataframe_data = dataframe_data.values.tolist()

    # Check if the list is empty
    if not dataframe_data or len(dataframe_data) == 0:
        return "数据为空", []

    conn = None
    try:
        file_name = Path(pdf_path).stem
        db_path = get_database_path(file_name)
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        # Clear all existing topics for this file
        cursor.execute('DELETE FROM topics WHERE file_name = ?', (file_name,))
        print(f"Cleared existing topics for file: {file_name}")

        inserted_count = 0
        skipped_count = 0

        # Insert all topics from DataFrame
        for row_idx, row in enumerate(dataframe_data):
            try:
                if len(row) < 6:  # Ensure we have all required columns
                    print(f"Row {row_idx + 1}: Insufficient columns ({len(row)} < 6), skipping")
                    skipped_count += 1
                    continue

                # Process each field with better validation
                topic_id = str(row[0]).strip() if row[0] is not None and str(row[0]).strip() else str(uuid.uuid4()).replace('-', '')[:8]
                file_name_col = str(row[1]).strip() if row[1] is not None and str(row[1]).strip() else file_name

                # Handle page_number conversion
                try:
                    page_number = int(float(str(row[2]))) if row[2] is not None and str(row[2]).strip() else 1
                except (ValueError, TypeError):
                    print(f"Row {row_idx + 1}: Invalid page_number '{row[2]}', using default 1")
                    page_number = 1

                topic_text = str(row[3]).strip() if row[3] is not None else ""

                # Handle topic_level conversion
                try:
                    topic_level = int(float(str(row[4]))) if row[4] is not None and str(row[4]).strip() else 1
                except (ValueError, TypeError):
                    print(f"Row {row_idx + 1}: Invalid topic_level '{row[4]}', using default 1")
                    topic_level = 1

                # Handle parent_topic_id - convert empty strings and 'None' to None
                parent_topic_id = None
                if row[5] is not None:
                    parent_str = str(row[5]).strip()
                    if parent_str and parent_str.lower() != 'none' and parent_str != 'nan':
                        parent_topic_id = parent_str

                if not topic_text:  # Skip empty topics
                    print(f"Row {row_idx + 1}: Empty topic_text, skipping")
                    skipped_count += 1
                    continue

                cursor.execute('''
                    INSERT INTO topics (topic_id, file_name, page_number, topic_text, topic_level, parent_topic_id)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (topic_id, file_name_col, page_number, topic_text, topic_level, parent_topic_id))
                inserted_count += 1

            except Exception as row_error:
                print(f"Row {row_idx + 1}: Error processing row {row}: {str(row_error)}")
                skipped_count += 1
                continue

        conn.commit()

        # Prepare result message
        result_msg = f"数据已同步到数据库，共插入 {inserted_count} 条记录"
        if skipped_count > 0:
            result_msg += f"，跳过 {skipped_count} 条无效记录"

        print(f"Sync completed: {inserted_count} inserted, {skipped_count} skipped")

        # Return the same data to preserve user edits
        return result_msg, dataframe_data

    except Exception as e:
        print(f"Error syncing DataFrame to database: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return f"同步失败: {str(e)}", []
    finally:
        if conn:
            conn.close()

# Constants and helper functions
def get_parse_method(is_ocr):
    """Return the parse method based on OCR flag"""
    return "ocr" if is_ocr else "auto"

def get_markdown_path(pdf_path, page_number):
    """Get the markdown path for a given PDF and page number"""
    pdf_stem = Path(pdf_path).stem
    return Path("output") / pdf_stem / f"page_{page_number}" / "ocr" / f"page_{page_number}.md"

# Functions copied from app_v5.py
# def read_fn(path):
#     disk_rw = FileBasedDataReader(os.path.dirname(path))
#     return disk_rw.read(os.path.basename(path))

# def parse_pdf(doc_path, output_dir, end_page_id, is_ocr, layout_mode, formula_enable, table_enable, language):
#     os.makedirs(output_dir, exist_ok=True)
#     print(f"{doc_path=}")
#     print(f"{output_dir=}")

#     try:
#         file_name = f"{str(Path(doc_path).stem)}"
#         pdf_data = read_fn(doc_path)
#         parse_method = get_parse_method(is_ocr)
#         local_image_dir, local_md_dir = prepare_env(output_dir, file_name, parse_method)
#         print(f"{local_image_dir=}")
#         print(f"{local_md_dir=}")

#         do_parse(
#             output_dir,
#             file_name,
#             pdf_data,
#             [],
#             parse_method,
#             False,
#             end_page_id=end_page_id,
#             layout_model=layout_mode,
#             formula_enable=formula_enable,
#             table_enable=table_enable,
#             lang=language,
#             f_dump_orig_pdf=False,
#         )
#         return local_md_dir, file_name
#     except Exception as e:
#         logger.exception(e)
#         return None, None

# Placeholder function for testing
def parse_pdf(doc_path, output_dir, end_page_id, is_ocr, layout_mode, formula_enable, table_enable, language):
    """Placeholder function for testing without magic_pdf dependency"""
    print("PDF parsing is disabled - magic_pdf module not available")
    return None, None

# Functions to handle image conversion for markdown
def image_to_base64(image_path):
    """Convert an image file to a base64 encoded string"""
    print(f"{image_path=}")
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def replace_image_with_base64(markdown_text, image_dir_path):
    """Replace image paths in markdown with base64-encoded images"""
    # Match Markdown image tags
    pattern = r'\!\[(?:[^\]]*)\]\(([^)]+)\)'

    print(f"{markdown_text=}")
    print(f"{image_dir_path=}")

    # Replace image links
    def replace(match):
        relative_path = match.group(1)
        print(f"{relative_path=}")
        # Handle both relative and absolute paths
        if os.path.isabs(relative_path):
            full_path = relative_path
            print(f"{full_path=}")
        else:
            # Normalize the path to handle multiple './' occurrences
            full_path = os.path.normpath(os.path.join(image_dir_path, relative_path))
            print(f"{full_path=}")
            # Ensure we don't have duplicate directory components
            full_path = os.path.abspath(full_path)
            print(f"{full_path=}")

        if os.path.exists(full_path):
            print(f"{full_path=}")
            base64_image = image_to_base64(full_path)
            # print(f"{base64_image=}")
            return f"![{relative_path}](data:image/jpeg;base64,{base64_image})"
        else:
            # If file doesn't exist, return original markdown (image won't display)
            return match.group(0)

    # Apply replacement
    return re.sub(pattern, replace, markdown_text)

def detect_rotation_angle(img):
    """
    通过文字方向检测图片旋转角度
    返回: 0 (正常), 90, 180, 270
    """
    print("in detect_rotation_angle()")
    try:
        # 转换为OpenCV格式 (BGR)
        img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

        # 转换为灰度图
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

        # 图像预处理 - 提高OCR精度
        # 1. 二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)
        # 2. 去噪
        denoised = cv2.fastNlMeansDenoising(binary, None, 10, 7, 21)
        # 3. 确保图像足够大
        height, width = denoised.shape
        if min(height, width) < 100:  # 如果图像太小，放大
            scale = 300.0 / min(height, width)
            denoised = cv2.resize(denoised, None, fx=scale, fy=scale, interpolation=cv2.INTER_CUBIC)

        # 使用Tesseract检测方向，添加PSM模式为6（假设为单一统一文本块）
        try:
            osd = pytesseract.image_to_osd(
                denoised,
                output_type=pytesseract.Output.DICT,
                config='--psm 0 -c min_characters_to_try=5'
            )
            print(f"OSD data: {osd}")
            return int(osd.get('rotate', 0))
        except Exception as e:
            print(f"Tesseract OSD failed: {str(e)}. Defaulting to 0 degrees.")
            return 0

    except Exception as e:
        print(f"Error in detect_rotation_angle: {str(e)}")
        return 0  # 默认不旋转

def analyze_image_with_vlm(image_path):
    """
    使用VLM模型分析图片并返回描述文本
    这里是一个占位函数，实际实现需要根据具体的VLM模型接口进行修改
    """
    # 占位实现，实际应使用VLM模型进行图片分析
    # 例如：vlm_model = VLMModel()
    #       description = vlm_model.analyze(image_path)
    #       return description

    # 返回一个示例描述

    # Send the request to the OpenAI API
    default_prompt = """
    请根据图片内容，用中文描述图片内容。
    """
    with open(image_path, "rb") as image_file:
        image_data = image_file.read()
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        image_source = f"data:image/jpeg;base64,{image_base64}"

    max_retries = 5
    retry_count = 0

    # Initialize OpenAI model
    try:
        model_name = os.environ.get("OPENAI_MODEL_NAME", "gpt-4o")
        api_key = os.environ.get("OPENAI_API_KEY", "sk-your-api-key")
        base_url = os.environ.get("OPENAI_BASE_URL", "https://api.openai.com/v1")

        openai_model = ChatOpenAI(
            model=model_name,
            temperature=0,
            api_key=api_key,
            base_url=base_url,
            top_p=0.75,
            seed=42,
        )
    except Exception as e:
        return f"# 错误\n\n初始化OpenAI模型时发生错误: {str(e)}"

    while retry_count < max_retries:
        try:
            response = openai_model.invoke(
                input=[
                    HumanMessage(
                        content=[
                            {"type": "text", "text": default_prompt},
                            {"type": "image_url", "image_url": {"url": image_source}}
                        ]
                    )
                ]
            )
            return response.content
        except Exception as e:
            retry_count += 1
            if retry_count <= max_retries:
                print(f"Attempt {retry_count} failed with error: {str(e)}")
                if retry_count < max_retries:
                    wait_time = min(10, 2 ** retry_count)  # Exponential backoff with max 10 seconds
                    print(f"Retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
            else:
                print(f"All attempts failed with error: {str(e)}")
                return f"Error: All attempts failed to analyze the image: {str(e)}"

def process_pdf_page_with_rotation(pdf_path, page_num, temp_dir, log_message):
    """
    处理PDF页面的图片旋转并生成新的PDF
    修复后的版本，解决了图片置换的bug
    """
    try:
        # 打开原始PDF
        doc = fitz.open(pdf_path)
        page = doc.load_page(page_num)

        # 获取页面上的所有图片
        image_list = page.get_images(full=True)
        log_message(f"页面 {page_num + 1} 包含 {len(image_list)} 个图片")

        if not image_list:
            # 如果没有图片，直接保存原页面
            temp_page_path = os.path.join(temp_dir, f"page_{page_num + 1}.pdf")
            single_page_doc = fitz.open()
            single_page_doc.insert_pdf(doc, from_page=page_num, to_page=page_num)
            single_page_doc.save(temp_page_path)
            single_page_doc.close()
            doc.close()
            return temp_page_path

        # 检查是否有需要旋转的图片
        need_rotation = False
        for img in image_list:
            xref = img[0]
            base_image = doc.extract_image(xref)
            image_bytes = base_image["image"]
            pil_image = Image.open(io.BytesIO(image_bytes))
            rotation_angle = detect_rotation_angle(pil_image)
            if rotation_angle != 0:
                need_rotation = True
                break

        if not need_rotation:
            # 如果没有需要旋转的图片，直接返回原始页面
            temp_page_path = os.path.join(temp_dir, f"page_{page_num + 1}.pdf")
            single_page_doc = fitz.open()
            single_page_doc.insert_pdf(doc, from_page=page_num, to_page=page_num)
            single_page_doc.save(temp_page_path)
            single_page_doc.close()
            doc.close()
            return temp_page_path

        # 创建图片输出目录
        image_output_dir = os.path.join(temp_dir, f"page_{page_num + 1}_images")
        os.makedirs(image_output_dir, exist_ok=True)

        # 存储处理后的图片信息
        processed_images = []

        # 处理每个图片
        for img_index, img in enumerate(image_list):
            try:
                xref = img[0]
                base_image = doc.extract_image(xref)
                image_bytes = base_image["image"]

                # 将字节流转换为PIL图像
                pil_image = Image.open(io.BytesIO(image_bytes))

                # 检测图片旋转角度并进行校正
                rotation_angle = detect_rotation_angle(pil_image)
                log_message(f"图片 {img_index + 1} 检测到旋转角度: {rotation_angle}°")

                # 校正图片方向
                if rotation_angle != 0:
                    # 旋转图片以纠正方向
                    rotated_image = pil_image.rotate(-rotation_angle, expand=True)  # 注意这里使用负角度来纠正旋转
                    processed_image = rotated_image
                    log_message(f"图片 {img_index + 1} 已旋转 -{rotation_angle}° 进行校正")
                else:
                    processed_image = pil_image
                    log_message(f"图片 {img_index + 1} 无需旋转")

                # 保存处理后的图像
                output_path = os.path.join(image_output_dir, f"img_{img_index + 1}.png")
                processed_image.save(output_path)

                # 将图片转换为字节流以便插入PDF
                img_byte_arr = io.BytesIO()
                processed_image.save(img_byte_arr, format='PNG')
                img_byte_arr = img_byte_arr.getvalue()

                # 存储处理后的图片信息
                processed_images.append({
                    'index': img_index,
                    'original_img': img,
                    'processed_bytes': img_byte_arr,
                    'processed_size': processed_image.size
                })

            except Exception as e:
                log_message(f"处理图片 {img_index + 1} 时出错: {str(e)}")
                continue

        # 创建新的PDF页面
        temp_page_path = os.path.join(temp_dir, f"page_{page_num + 1}.pdf")

        # 方法1: 直接修改原PDF页面的图片
        try:
            # 创建页面的副本
            single_page_doc = fitz.open()
            single_page_doc.insert_pdf(doc, from_page=page_num, to_page=page_num)
            new_page = single_page_doc[0]

            # 获取页面上的图片区域并替换
            for processed_img in processed_images:
                try:
                    img_info = processed_img['original_img']
                    xref = img_info[0]

                    # 查找图片在页面上的位置
                    # 通过遍历页面的所有对象来找到图片的位置
                    image_rects = []
                    for block in new_page.get_text("dict")["blocks"]:
                        if "bbox" in block and block.get("type") == 1:  # 图片块
                            image_rects.append(fitz.Rect(block["bbox"]))

                    # 如果找到了图片区域，替换图片
                    if processed_img['index'] < len(image_rects):
                        rect = image_rects[processed_img['index']]
                        # 删除原图片区域的内容并插入新图片
                        new_page.add_redact_annot(rect)
                        new_page.apply_redactions()
                        new_page.insert_image(rect, stream=processed_img['processed_bytes'])
                        log_message(f"成功替换图片 {processed_img['index'] + 1}")
                    else:
                        # 如果无法确定位置，尝试替换PDF中的图片对象
                        # 这是一个更底层的方法
                        try:
                            # 获取原始图片的尺寸和位置
                            img_obj = single_page_doc.extract_image(xref)

                            # 计算合适的插入位置（页面中央）
                            page_rect = new_page.rect
                            img_width, img_height = processed_img['processed_size']

                            # 计算缩放比例以适应页面
                            scale_x = min(page_rect.width * 0.8 / img_width, 1.0)
                            scale_y = min(page_rect.height * 0.8 / img_height, 1.0)
                            scale = min(scale_x, scale_y)

                            new_width = img_width * scale
                            new_height = img_height * scale

                            # 居中放置
                            x = (page_rect.width - new_width) / 2
                            y = (page_rect.height - new_height) / 2

                            rect = fitz.Rect(x, y, x + new_width, y + new_height)
                            new_page.insert_image(rect, stream=processed_img['processed_bytes'])
                            log_message(f"在默认位置插入处理后的图片 {processed_img['index'] + 1}")

                        except Exception as fallback_e:
                            log_message(f"无法替换图片 {processed_img['index'] + 1}: {str(fallback_e)}")
                            continue

                except Exception as e:
                    log_message(f"替换图片 {processed_img['index'] + 1} 时出错: {str(e)}")
                    continue

            # 保存修改后的PDF
            single_page_doc.save(temp_page_path)
            single_page_doc.close()
            log_message(f"页面 {page_num + 1} 处理完成，已保存到 {temp_page_path}")

        except Exception as e:
            log_message(f"创建新PDF页面时出错: {str(e)}")
            # 如果失败，保存原始页面
            single_page_doc = fitz.open()
            single_page_doc.insert_pdf(doc, from_page=page_num, to_page=page_num)
            single_page_doc.save(temp_page_path)
            single_page_doc.close()

        doc.close()
        return temp_page_path

    except Exception as e:
        log_message(f"处理页面 {page_num + 1} 时发生严重错误: {str(e)}")
        # 返回原始页面
        try:
            doc = fitz.open(pdf_path)
            temp_page_path = os.path.join(temp_dir, f"page_{page_num + 1}.pdf")
            single_page_doc = fitz.open()
            single_page_doc.insert_pdf(doc, from_page=page_num, to_page=page_num)
            single_page_doc.save(temp_page_path)
            single_page_doc.close()
            doc.close()
            return temp_page_path
        except Exception as fallback_e:
            log_message(f"保存原始页面时也出错: {str(fallback_e)}")
            return None

def pdf_to_markdown(pdf_path, use_vlm=False, progress=gr.Progress()):
    """
    使用minerU技术将PDF转换为Markdown，每一页转换为一个Markdown文件
    修复了图片置换的bug

    参数:
        pdf_path (str): PDF文件路径
        use_vlm (bool): 是否使用VLM解析Markdown中的图片
        progress: Gradio进度条和日志记录器

    返回:
        tuple: (转换后的Markdown内容, 日志内容)
    """
    log_messages = []

    def log_message(message):
        """记录日志信息"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        log_messages.append(log_entry)
        # 更新进度条，这里我们简单地使用日志消息数量作为进度
        progress((len(log_messages), 50))  # 假设总共有50个日志消息
        return "\n".join(log_messages)

    try:
        log_message("开始转换PDF文件...")

        # 获取文件信息
        file_name = Path(pdf_path).name
        file_size = os.path.getsize(pdf_path) / (1024 * 1024)  # 转换为MB
        log_message(f"正在处理文件: {file_name} (大小: {file_size:.2f}MB)")

        # 创建输出目录
        output_base_dir = Path("output")
        output_base_dir.mkdir(exist_ok=True)

        # 创建PDF专属目录
        pdf_output_dir = output_base_dir / Path(pdf_path).stem
        if pdf_output_dir.exists():
            log_message(f"删除已存在的目录: {pdf_output_dir}")
            import shutil
            shutil.rmtree(pdf_output_dir)
        pdf_output_dir.mkdir()

        # 使用minerU技术解析PDF
        log_message("使用minerU技术解析PDF文件...")
        end_page_id = None  # 处理所有页面
        is_ocr = True  # 启用OCR
        layout_mode = "doclayout_yolo"  # 使用doclayout_yolo布局模型
        formula_enable = True  # 启用公式识别
        table_enable = True  # 启用表格识别
        language = "ch"  # 使用中文

        # 打开PDF文件并获取页数
        doc = fitz.open(pdf_path)
        page_count = len(doc)
        log_message(f"PDF文件共包含 {page_count} 页")
        doc.close()

        # 创建临时目录用于存储中间文件
        with tempfile.TemporaryDirectory() as temp_dir:
            for page_num in range(page_count):
                log_message(f"开始处理第 {page_num + 1} 页...")

                # 使用修复后的图片处理函数
                temp_page_path = process_pdf_page_with_rotation(pdf_path, page_num, temp_dir, log_message)

                if temp_page_path is None:
                    log_message(f"第 {page_num + 1} 页处理失败，跳过")
                    continue

                # 使用minerU技术解析处理后的PDF页面
                try:
                    local_md_dir, file_name = parse_pdf(
                        temp_page_path,
                        str(pdf_output_dir),
                        None,  # 处理所有页面
                        True,  # 启用OCR
                        "doclayout_yolo",  # 使用doclayout_yolo布局模型
                        True,  # 启用公式识别
                        True,  # 启用表格识别
                        "ch"  # 使用中文
                    )
                    if local_md_dir and file_name:
                        print(f"{local_md_dir=}")
                        print(f"{file_name=}")
                        log_message(f"第 {page_num + 1} 页minerU解析完成")
                    else:
                        log_message(f"parse_pdf returned invalid values for page {page_num + 1}")
                except Exception as e:
                    log_message(f"Failed to parse PDF for page {page_num + 1}: {e}")
                    continue

                # 读取Markdown内容
                md_file_path = Path(local_md_dir) / f"{file_name}.md"
                print(f"{md_file_path=}")
                if not md_file_path.exists():
                    log_message(f"第 {page_num + 1} 页转换失败！未找到对应的Markdown文件。")
                    continue

                # 读取Markdown文件内容
                with open(md_file_path, 'r', encoding='utf-8') as f:
                    markdown_content = f.read()

                # 如果启用了VLM图片解析，处理图片
                if use_vlm:
                    # 使用正则表达式找到所有图片路径
                    image_pattern = r'\!\[(.*?)\]\((.*?)\)'
                    image_matches = re.findall(image_pattern, markdown_content)
                    print(f"{image_matches=}")

                    for alt_text, image_path in image_matches:
                        # Handle both relative and absolute paths
                        if os.path.isabs(image_path):
                            full_path = image_path
                            print(f"{full_path=}")
                        else:
                            # Normalize the path to handle multiple './' occurrences
                            full_path = os.path.normpath(os.path.join(os.path.dirname(md_file_path), image_path))
                            print(f"{full_path=}")
                            # Ensure we don't have duplicate directory components
                            full_path = os.path.abspath(full_path)
                            print(f"{full_path=}")

                        # Use the resolved full_path for VLM analysis
                        if os.path.exists(full_path):
                            log_message(f"使用VLM分析图片: {os.path.basename(full_path)}")
                            vlm_description = analyze_image_with_vlm(full_path)
                            print(f"{vlm_description=}")

                            # 将描述添加到图片后面
                            markdown_content = markdown_content.replace(
                                f"![{alt_text}]({image_path})",
                                f"![{alt_text}]({image_path})\n\n<图片描述>\n\n{vlm_description.strip()}\n\n</图片描述>"
                            )

                # 将处理后的内容写回文件
                with open(md_file_path, 'w', encoding='utf-8') as f:
                    f.write(markdown_content)

                # 提取主题并保存到数据库
                try:
                    # 初始化该文件的数据库
                    init_database(Path(pdf_path).stem)
                    topics = extract_topics_from_markdown(markdown_content, Path(pdf_path).stem, page_num + 1)
                    save_topics_to_database(topics, Path(pdf_path).stem)
                    log_message(f"第 {page_num + 1} 页提取到 {len(topics)} 个主题并保存到数据库")
                except Exception as e:
                    log_message(f"第 {page_num + 1} 页保存主题到数据库时出错: {str(e)}")

                log_message(f"第 {page_num + 1} 页转换完成！文件已保存至: {md_file_path}")

        log_message("所有页面转换完成！")
        log_message("=" * 50)

        return "\n".join(log_messages)

    except Exception as e:
        error_msg = f"转换PDF时发生错误: {str(e)}"
        log_message(error_msg)
        import traceback
        log_message(traceback.format_exc())
        return f"# 错误\n\n{error_msg}" + "\n".join(log_messages)

def process_current_file_to_database(pdf_path, progress=gr.Progress()):
    """
    处理当前上传文件的 Markdown 文件并将主题信息保存到数据库

    参数:
        pdf_path: 当前上传的PDF文件路径
        progress: Gradio进度条和日志记录器

    返回:
        str: 处理结果日志
    """
    log_messages = []

    def log_message(message):
        """记录日志信息"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        log_messages.append(log_entry)
        # 更新进度条
        progress((len(log_messages), 100))  # 假设总共有100个日志消息
        return "\n".join(log_messages)

    try:
        if not pdf_path:
            log_message("请先上传PDF文件")
            return "\n".join(log_messages)

        # 获取文件名
        file_name = Path(pdf_path).stem
        log_message(f"开始处理当前文件: {file_name}")

        # 检查输出目录是否存在
        output_dir = Path("output")
        pdf_dir = output_dir / file_name

        if not pdf_dir.exists():
            log_message(f"未找到文件 {file_name} 的输出目录，请先进行PDF转换")
            return "\n".join(log_messages)

        # 为该文件初始化数据库
        try:
            init_database(file_name)
            log_message(f"为文件 {file_name} 初始化数据库")
        except Exception as e:
            log_message(f"初始化数据库失败: {str(e)}")
            return "\n".join(log_messages)

        total_topics = 0
        page_count = 0

        # 查找页面目录
        for page_dir in pdf_dir.iterdir():
            if page_dir.is_dir() and page_dir.name.startswith('page_'):
                page_number = int(page_dir.name.split('_')[1])

                # 查找 OCR 目录
                ocr_dir = page_dir / "ocr"
                if ocr_dir.exists():
                    md_file = ocr_dir / f"page_{page_number}.md"

                    if md_file.exists():
                        log_message(f"  处理第 {page_number} 页")
                        page_count += 1

                        try:
                            # 读取 Markdown 内容
                            with open(md_file, 'r', encoding='utf-8') as f:
                                markdown_content = f.read()

                            # 提取主题
                            topics = extract_topics_from_markdown(
                                markdown_content,
                                file_name,
                                page_number
                            )

                            # 保存到数据库
                            if topics:
                                save_topics_to_database(topics, file_name)
                                total_topics += len(topics)
                                log_message(f"    第 {page_number} 页提取到 {len(topics)} 个主题")
                            else:
                                log_message(f"    第 {page_number} 页未找到主题")

                        except Exception as e:
                            log_message(f"    处理第 {page_number} 页时出错: {str(e)}")

        if page_count == 0:
            log_message(f"未找到文件 {file_name} 的Markdown文件，请先进行PDF转换")
            return "\n".join(log_messages)
        else:
            log_message(f"文件 {file_name} 处理完成，共处理 {page_count} 页，提取 {total_topics} 个主题")

        log_message("=" * 50)

        return "\n".join(log_messages)

    except Exception as e:
        error_msg = f"处理当前文件时发生错误: {str(e)}"
        log_message(error_msg)
        import traceback
        log_message(traceback.format_exc())
        return "\n".join(log_messages)

def display_pdf_page(pdf_path, page_number=1):
    """
    显示PDF页面为图片

    参数:
        pdf_path (str): PDF文件路径
        page_number (int): 要显示的页码（从1开始）

    返回:
        str: 生成的图片路径
    """
    try:
        # Check if the PDF file exists
        if not os.path.exists(pdf_path):
            print(f"Error: PDF file not found at path: {pdf_path}")
            return None

        doc = fitz.open(pdf_path)
        page = doc.load_page(page_number - 1)  # PyMuPDF的页码从0开始
        pix = page.get_pixmap(dpi=200)  # 增加DPI以获得更好的质量

        # 确保输出目录存在
        # 确保输出目录存在
        pdf_output_dir = Path("output") / Path(pdf_path).stem
        img_dir = pdf_output_dir.parent / (pdf_output_dir.name + "_images")
        img_dir.mkdir(parents=True, exist_ok=True)

        # 使用PDF文件名作为前缀
        output_path = img_dir / f"page_{page_number}.png"
        pix.save(str(output_path))
        return str(output_path)
    except Exception as e:
        print(f"Error displaying PDF page: {e}")
        return None

# 异步流式调用OpenAI LLM的封装
async def stream_openai_check(markdown_content: str, checkpoint: str, minutes_input: str, model: ChatOpenAI):
    prompt = f"""
    {checkpoint}

    Minutes Input:
    {minutes_input}

    Markdown Content:
    {markdown_content}

    Provide a detailed analysis of whether the content meets the requirements specified in the checkpoint.
    """
    messages = [SystemMessage(content=checkpoint), HumanMessage(content=prompt)]

    # Retry mechanism with exponential backoff
    max_retries = 5
    retry_count = 0

    while retry_count < max_retries:
        try:
            async for chunk in model.astream(messages):
                yield chunk.content
            # If we get here, the request was successful
            break
        except Exception as e:
            retry_count += 1
            if retry_count <= max_retries:
                print(f"Attempt {retry_count} failed with error: {str(e)}")
                if retry_count < max_retries:
                    wait_time = min(10, 2 ** retry_count)  # Exponential backoff with max 10 seconds
                    print(f"Retrying in {wait_time} seconds...")
                    await asyncio.sleep(wait_time)
            else:
                print(f"All attempts failed with error: {str(e)}")
                yield f"Error: All attempts failed to analyze the content: {str(e)}"
                return

def create_app():
    """创建并返回Gradio应用实例"""

    with gr.Blocks(title="PDF to Markdown Converter") as demo:
        gr.Markdown("# PDF to Markdown Converter")

        with gr.Tabs():
            # Combined Tab: PDF Upload, Conversion, and Comparison
            with gr.TabItem("PDF 转换与对比"):
                # Layout with three columns
                with gr.Row():
                    # Left Column: PDF Upload and Controls
                    with gr.Column(scale=1):
                        # 文件上传区域
                        pdf_input = gr.File(label="上传PDF文件", file_types=[".pdf"])

                        # 使用VLM解析图片的复选框
                        use_vlm_checkbox = gr.Checkbox(label="使用VLM解析Markdown中的图片", value=False)

                        # 按钮和日志布局
                        with gr.Row():
                            convert_btn = gr.Button("🚀 开始转换", variant="primary")
                            process_existing_btn = gr.Button("💾 数据存储", variant="secondary")
                    with gr.Column(scale=2):
                        # 日志输出区域
                        log_output = gr.TextArea(
                            label="转换日志",
                            lines=8,
                            max_lines=8,
                            interactive=False,
                            show_copy_button=True,
                            autoscroll=True,  # 自动滚动到底部
                            container=False,  # 不使用容器
                        )

                with gr.Row():
                    # Middle Column: PDF Display
                    with gr.Column(scale=1):
                        # PDF Page Display
                        with gr.Row():
                            page_slider = gr.Slider(minimum=1, maximum=1, step=1, label="Page Number", visible=False, container=True)
                        with gr.Row():
                            pdf_image = gr.Image(label="PDF Page", interactive=False, height=600, container=False)

                    # Right Column: Markdown Modify
                    with gr.Column(scale=1):
                        # Markdown Content Modify
                        gr.Markdown("&nbsp;")
                        gr.Markdown("Edit Content")
                        markdown_content_text = gr.TextArea(
                            label="Markdown Content Text",
                            elem_id="markdown_content_text",
                            lines=30,
                            max_lines=30,
                            container=False,
                            interactive=True,
                            autoscroll=False,
                        )

                    # Right Column: Markdown Display
                    with gr.Column(scale=1):
                        gr.Markdown("&nbsp;")
                        gr.Markdown("Content Preview")
                        markdown_content_preview = gr.Markdown(label="Markdown Content Preview", elem_id="markdown_content", height=600, max_height=600)

                # Add some spacing between sections
                gr.Markdown("&nbsp;")

                # 数据库操作函数
                def refresh_topics_data(pdf_path):
                    """刷新主题数据"""
                    if not pdf_path:
                        return []

                    file_name = Path(pdf_path).stem
                    return load_topics_from_database(file_name)

                def on_dataframe_select(evt: gr.SelectData):
                    """处理DataFrame行选择事件"""
                    return [evt.index[0]]  # 返回选中行的索引

                def on_topics_dataframe_change(pdf_path, data):
                    """处理主要DataFrame数据变化事件 - 立即保存到数据库并刷新"""
                    try:
                        if hasattr(data, 'empty'):
                            # pandas DataFrame
                            row_count = 0 if data.empty else len(data)
                        elif data is None:
                            row_count = 0
                        elif isinstance(data, list):
                            # list
                            row_count = len(data)
                        else:
                            # other iterable
                            row_count = len(data) if data else 0
                        print(f"Main DataFrame changed, saving to database: {row_count} rows (type: {type(data)})")

                        # 立即保存到数据库
                        if pdf_path and data is not None:
                            sync_result, _ = sync_dataframe_to_database(pdf_path, data)
                            print(f"Immediate save result: {sync_result}")

                            # 立即从数据库刷新数据（相当于调用"🔄 刷新数据"功能）
                            file_name = Path(pdf_path).stem
                            refreshed_data = load_topics_from_database(file_name)
                            print(f"Refreshed data after immediate save: {len(refreshed_data)} rows")
                            return refreshed_data

                    except Exception as e:
                        print(f"Main DataFrame changed, error: {e}")

                    return data  # 如果出错，返回原数据

                def load_topics_data_for_main_dataframe(pdf_path):
                    """为主要DataFrame加载主题数据 - 直接从数据库加载，不考虑编辑状态"""
                    if not pdf_path:
                        return []

                    file_name = Path(pdf_path).stem
                    data = load_topics_from_database(file_name)
                    print(f"Loading data from database for main dataframe: {len(data)} rows")
                    return data

                def delete_selected_main_rows(pdf_path, selected_indices, current_dataframe_data):
                    """删除主要数据库表格中选中的行 - 从当前dataframe数据中删除，不重新加载数据库"""
                    if not pdf_path:
                        return "请先上传PDF文件", current_dataframe_data or []

                    # Check if any rows are selected
                    if not selected_indices or len(selected_indices) == 0:
                        return "请先选择要删除的行", current_dataframe_data or []

                    # Check if current dataframe data exists
                    if current_dataframe_data is None:
                        return "当前没有数据", []

                    # Handle pandas DataFrame
                    if hasattr(current_dataframe_data, 'empty'):
                        if current_dataframe_data.empty:
                            return "当前没有数据", []
                    elif isinstance(current_dataframe_data, list) and len(current_dataframe_data) == 0:
                        return "当前没有数据", []

                    try:
                        # Convert to list if it's a pandas DataFrame
                        if hasattr(current_dataframe_data, 'values'):
                            data_list = current_dataframe_data.values.tolist()
                        else:
                            data_list = list(current_dataframe_data)

                        # Extract topic_ids of selected rows for database deletion
                        topic_ids_to_delete = []
                        for idx in selected_indices:
                            if 0 <= idx < len(data_list):
                                topic_id = str(data_list[idx][0]).strip()  # First column is topic_id
                                if topic_id:
                                    topic_ids_to_delete.append(topic_id)

                        if not topic_ids_to_delete:
                            return "没有找到有效的主题ID", current_dataframe_data

                        # Delete from database
                        file_name = Path(pdf_path).stem
                        db_path = get_database_path(file_name)
                        db_connection = sqlite3.connect(str(db_path))
                        db_cursor = db_connection.cursor()

                        placeholders = ','.join(['?' for _ in topic_ids_to_delete])
                        db_cursor.execute(f"DELETE FROM topics WHERE topic_id IN ({placeholders})", topic_ids_to_delete)
                        db_connection.commit()
                        deleted_count = db_cursor.rowcount
                        db_connection.close()

                        print(f"Deleted {deleted_count} topics from database: {topic_ids_to_delete}")

                        # Remove selected rows from current data (in reverse order to maintain indices)
                        for idx in sorted(selected_indices, reverse=True):
                            if 0 <= idx < len(data_list):
                                data_list.pop(idx)

                        # Return updated data without reloading from database
                        return f"已删除 {deleted_count} 条记录", data_list

                    except Exception as e:
                        print(f"Error deleting selected rows: {e}")
                        return f"删除失败: {str(e)}", current_dataframe_data or []

                # 文件上传后自动填充日志
                def on_file_upload(file):
                    """文件上传后的回调函数"""
                    if file:
                        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        vlm_status = "启用" if use_vlm_checkbox else "未启用"
                        return f"[{timestamp}] 文件已上传，准备转换...\nVLM图片解析: {vlm_status}\n请点击'开始转换'按钮以继续"
                    return "未选择文件"

                # Function to handle markdown content changes
                def on_markdown_content_change(pdf_path, page_number, new_content):
                    """Handle changes in markdown content text"""
                    print("in on_markdown_content_change()")
                    if not pdf_path or page_number < 1:
                        return new_content

                    try:
                        # Get the markdown file path using helper function
                        markdown_path = get_markdown_path(pdf_path, page_number)
                        os.makedirs(os.path.dirname(markdown_path), exist_ok=True)

                        # Save the content to the file
                        with open(markdown_path, 'w', encoding='utf-8') as f:
                            f.write(new_content)

                        # Convert images to base64 for preview display
                        preview_content = replace_image_with_base64(new_content, str(os.path.dirname(markdown_path)))

                        # Return the updated content for display
                        return preview_content
                    except Exception as e:
                        print(f"Error saving markdown content: {e}")
                        return new_content

                # 事件处理器
                def update_pdf_display(pdf_path):
                    """更新PDF显示，设置滑块的最大值"""
                    if pdf_path is None:
                        return None, gr.update(visible=False), "未选择PDF文件"

                    try:
                        # 打开PDF获取页数
                        doc = fitz.open(pdf_path)
                        page_count = len(doc)

                        # 默认显示第一页
                        first_page_path = display_pdf_page(pdf_path, 1)

                        # Check if markdown file exists for the first page
                        pdf_stem = Path(pdf_path).stem
                        # Get the markdown path for page 1
                        markdown_path = get_markdown_path(pdf_path, 1)
                        markdown_content = ""

                        if markdown_path.exists():
                            with open(markdown_path, 'r', encoding='utf-8') as f:
                                markdown_content = f.read()

                        return first_page_path, gr.update(maximum=page_count, value=1, visible=True), markdown_content
                    except Exception as e:
                        print(f"Error updating PDF display: {e}")
                        return None, gr.update(visible=False), f"无法加载PDF: {str(e)}"

                def update_page(pdf_path, page_number):
                    """当页码改变时更新页面显示和对应的Markdown内容"""
                    if pdf_path is None or page_number < 1:
                        return None, "未选择PDF文件或页码无效", "未选择PDF文件或页码无效"

                    try:
                        # 显示指定页面
                        image_path = display_pdf_page(pdf_path, page_number)

                        # 获取该页的Markdown内容
                        # Get the markdown path for the current page
                        markdown_path = get_markdown_path(pdf_path, page_number)
                        print(f"{markdown_path=}")

                        if markdown_path.exists():
                            with open(markdown_path, 'r', encoding='utf-8') as f:
                                markdown_content = f.read()
                        else:
                            markdown_content = f"# 第{page_number}页内容\n\n未找到对应的Markdown文件。"

                        return image_path, markdown_content
                    except Exception as e:
                        print(f"Error updating page: {e}")
                        return None, "# 错误\n\n显示页面时发生错误: " + str(e)

                pdf_input.upload(
                    fn=on_file_upload,
                    inputs=pdf_input,
                    outputs=log_output
                )

                # Update the PDF display when the file is uploaded
                pdf_input.change(
                    fn=update_pdf_display,
                    inputs=pdf_input,
                    outputs=[pdf_image, page_slider, markdown_content_text]
                )

                # 转换按钮点击事件
                convert_btn.click(
                    fn=pdf_to_markdown,
                    inputs=[pdf_input, use_vlm_checkbox],
                    outputs=[log_output],
                    show_progress="full",
                ).then(
                    fn=update_pdf_display,
                    inputs=pdf_input,
                    outputs=[pdf_image, page_slider, markdown_content_text]
                )

                # 数据存储按钮点击事件
                process_existing_btn.click(
                    fn=process_current_file_to_database,
                    inputs=[pdf_input],
                    outputs=[log_output],
                    show_progress="full",
                )

                page_slider.release(
                    fn=update_page,
                    inputs=[pdf_input, page_slider],
                    outputs=[pdf_image, markdown_content_text]
                )

                # Add event listener for markdown content text changes
                markdown_content_text.change(
                    fn=on_markdown_content_change,
                    inputs=[pdf_input, page_slider, markdown_content_text],
                    outputs=[markdown_content_preview],
                    show_progress="hidden",
                )

            # New Tab: Database Topics Viewer
            with gr.TabItem("主题数据库"):
                # Layout with controls and table
                with gr.Row():
                    # Left Column: Controls
                    with gr.Column(scale=1):
                        # Database file selector
                        db_file_selector = gr.Dropdown(
                            label="选择数据库文件",
                            choices=[],
                            container=False,
                        )

                        # Action buttons
                        with gr.Row():
                            refresh_btn = gr.Button("🔄 刷新数据", variant="secondary", size="sm")

                        with gr.Row():
                            delete_selected_btn = gr.Button("🗑️ 删除选中行", variant="stop", size="sm")
                            clear_btn = gr.Button("🧹 清空数据库", variant="stop", size="sm")

                        # Status display
                        status_output = gr.Textbox(
                            label="操作状态",
                            lines=3,
                            max_lines=3,
                            interactive=False,
                            container=False,
                        )

                    # Right Column: Table
                    with gr.Column(scale=3):
                        # Topics table
                        topics_table = gr.DataFrame(
                            headers=["主题ID", "文件名", "页数", "主题内容", "层次", "父主题ID"],
                            datatype=["str", "str", "number", "str", "number", "str"],
                            label="主题数据",
                            show_label=False,
                            interactive=True,
                            wrap=True,
                            row_count=(1, "dynamic"),
                        )

                # 用于存储选中行索引的状态（主题数据库标签页）
                selected_rows_state_topics = gr.State([])

                # Function to get available database files
                def get_available_databases():
                    """Get list of available database files"""
                    output_dir = Path("output")
                    db_files = []

                    if output_dir.exists():
                        for item in output_dir.iterdir():
                            if item.is_dir() and item.name.endswith('_db'):
                                db_file = item / "topics.db"
                                if db_file.exists():
                                    # Extract file name from directory name
                                    file_name = item.name[:-3]  # Remove '_db' suffix
                                    db_files.append((file_name, str(db_file)))

                    return db_files

                # Function to load topics from database
                def load_topics_from_database(selected_db=None):
                    """Load topics from database"""
                    if not selected_db:
                        return []

                    try:
                        # Find the database path for the selected file
                        db_files = get_available_databases()
                        db_path = None
                        for file_name, path in db_files:
                            if file_name == selected_db:
                                db_path = path
                                break

                        if not db_path:
                            return []

                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()

                        # Simple query without filters
                        query = "SELECT topic_id, file_name, page_number, topic_text, topic_level, parent_topic_id FROM topics ORDER BY file_name, page_number, topic_level"

                        cursor.execute(query)
                        results = cursor.fetchall()
                        conn.close()

                        return results
                    except Exception as e:
                        print(f"Error loading topics from database: {e}")
                        return []

                # Function to update single topic in database
                def update_single_topic_in_database(selected_db, topic_id, file_name, page_number, topic_text, topic_level, parent_topic_id):
                    """Update or insert a single topic in the database"""
                    if not selected_db:
                        return False

                    conn = None
                    try:
                        # Find the database path for the selected file
                        db_files = get_available_databases()
                        db_path = None
                        for file_name_db, path in db_files:
                            if file_name_db == selected_db:
                                db_path = path
                                break

                        if not db_path:
                            print(f"Database path not found for: {selected_db}")
                            return False

                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()

                        # Check if topic exists
                        cursor.execute('SELECT COUNT(*) FROM topics WHERE topic_id = ?', (topic_id,))
                        exists = cursor.fetchone()[0] > 0

                        if exists:
                            # Update existing topic
                            cursor.execute('''
                                UPDATE topics
                                SET file_name = ?, page_number = ?, topic_text = ?, topic_level = ?, parent_topic_id = ?
                                WHERE topic_id = ?
                            ''', (file_name, page_number, topic_text, topic_level, parent_topic_id, topic_id))
                            print(f"Updated topic: {topic_id}")
                        else:
                            # Insert new topic
                            cursor.execute('''
                                INSERT INTO topics (topic_id, file_name, page_number, topic_text, topic_level, parent_topic_id)
                                VALUES (?, ?, ?, ?, ?, ?)
                            ''', (topic_id, file_name, page_number, topic_text, topic_level, parent_topic_id))
                            print(f"Inserted new topic: {topic_id}")

                        conn.commit()
                        return True

                    except Exception as e:
                        print(f"Error updating single topic: {e}")
                        if conn:
                            conn.rollback()
                        return False
                    finally:
                        if conn:
                            conn.close()

                # Function to handle topics table data changes
                def on_topics_table_change(selected_db, data):
                    """处理主题数据库表格数据变化事件 - 增量更新到数据库"""
                    try:
                        if not selected_db or data is None:
                            return data

                        # Convert to list if it's a pandas DataFrame
                        if hasattr(data, 'values'):
                            data_list = data.values.tolist()
                        else:
                            data_list = list(data) if data else []

                        if not data_list:
                            return data

                        print(f"Topics table changed, processing {len(data_list)} rows for incremental update")

                        # Process each row for incremental update
                        updated_count = 0
                        for row_idx, row in enumerate(data_list):
                            try:
                                if len(row) < 6:  # Ensure we have all required columns
                                    continue

                                # Process each field with validation
                                topic_id = str(row[0]).strip() if row[0] is not None and str(row[0]).strip() else str(uuid.uuid4()).replace('-', '')[:8]
                                file_name_col = str(row[1]).strip() if row[1] is not None and str(row[1]).strip() else selected_db

                                # Handle page_number conversion
                                try:
                                    page_number = int(float(str(row[2]))) if row[2] is not None and str(row[2]).strip() else 1
                                except (ValueError, TypeError):
                                    page_number = 1

                                topic_text = str(row[3]).strip() if row[3] is not None else ""

                                # Handle topic_level conversion
                                try:
                                    topic_level = int(float(str(row[4]))) if row[4] is not None and str(row[4]).strip() else 1
                                except (ValueError, TypeError):
                                    topic_level = 1

                                # Handle parent_topic_id
                                parent_topic_id = None
                                if row[5] is not None:
                                    parent_str = str(row[5]).strip()
                                    if parent_str and parent_str.lower() != 'none' and parent_str != 'nan':
                                        parent_topic_id = parent_str

                                if topic_text:  # Only update non-empty topics
                                    if update_single_topic_in_database(selected_db, topic_id, file_name_col, page_number, topic_text, topic_level, parent_topic_id):
                                        updated_count += 1

                            except Exception as row_error:
                                print(f"Row {row_idx + 1}: Error processing row: {str(row_error)}")
                                continue

                        print(f"Incremental update completed: {updated_count} topics updated")
                        return data  # Return the same data to preserve user edits

                    except Exception as e:
                        print(f"Topics table changed, error: {e}")
                        import traceback
                        print(f"Full traceback: {traceback.format_exc()}")

                    return data  # 如果出错，返回原数据

                def load_topics_data_for_topics_table(selected_db):
                    """为主题数据库表格加载数据 - 直接从数据库加载，不考虑编辑状态"""
                    if not selected_db:
                        return []

                    data = load_topics_from_database(selected_db)
                    print(f"Loading data from database for topics table: {len(data)} rows")
                    return data

                def on_topics_table_select(evt: gr.SelectData):
                    """处理主题数据库表格行选择事件"""
                    return [evt.index[0]]  # 返回选中行的索引



                # Note: sync_table_to_database function removed - using incremental updates instead

                # Function to delete selected rows
                def delete_selected_rows(selected_db, selected_indices, current_table_data):
                    """删除主题数据库表格中选中的行"""
                    if not selected_db:
                        return "请先选择数据库文件", current_table_data or []

                    # Check if any rows are selected
                    if not selected_indices or len(selected_indices) == 0:
                        return "请先选择要删除的行", current_table_data or []

                    # Check if current table data exists
                    if current_table_data is None:
                        return "当前没有数据", []

                    # Handle pandas DataFrame
                    if hasattr(current_table_data, 'empty'):
                        if current_table_data.empty:
                            return "当前没有数据", []
                    elif isinstance(current_table_data, list) and len(current_table_data) == 0:
                        return "当前没有数据", []

                    try:
                        # Convert to list if it's a pandas DataFrame
                        if hasattr(current_table_data, 'values'):
                            data_list = current_table_data.values.tolist()
                        else:
                            data_list = list(current_table_data)

                        # Extract topic_ids of selected rows for database deletion
                        topic_ids_to_delete = []
                        for idx in selected_indices:
                            if 0 <= idx < len(data_list):
                                topic_id = str(data_list[idx][0]).strip()  # First column is topic_id
                                if topic_id:
                                    topic_ids_to_delete.append(topic_id)

                        if not topic_ids_to_delete:
                            return "没有找到有效的主题ID", current_table_data

                        # Find the database path for the selected file
                        db_files = get_available_databases()
                        db_path = None
                        for file_name, path in db_files:
                            if file_name == selected_db:
                                db_path = path
                                break

                        if not db_path:
                            return "未找到数据库文件", current_table_data

                        # Delete from database
                        db_connection = sqlite3.connect(db_path)
                        db_cursor = db_connection.cursor()

                        placeholders = ','.join(['?' for _ in topic_ids_to_delete])
                        db_cursor.execute(f"DELETE FROM topics WHERE topic_id IN ({placeholders})", topic_ids_to_delete)
                        db_connection.commit()
                        deleted_count = db_cursor.rowcount
                        db_connection.close()

                        print(f"Deleted {deleted_count} topics from database: {topic_ids_to_delete}")

                        # Reload data from database to ensure consistency
                        refreshed_data = load_topics_from_database(selected_db)
                        return f"已删除 {deleted_count} 条记录", refreshed_data

                    except Exception as e:
                        print(f"Error deleting selected rows: {e}")
                        return f"删除失败: {str(e)}", current_table_data or []

                # Function to clear database
                def clear_database(selected_db=None):
                    """Clear all topics from selected database"""
                    if not selected_db:
                        return "请先选择数据库文件", []

                    try:
                        # Find the database path for the selected file
                        db_files = get_available_databases()
                        db_path = None
                        for file_name, path in db_files:
                            if file_name == selected_db:
                                db_path = path
                                break

                        if not db_path:
                            return "未找到数据库文件", []

                        db_connection = sqlite3.connect(db_path)
                        db_cursor = db_connection.cursor()

                        # Count records before deletion
                        db_cursor.execute("SELECT COUNT(*) FROM topics")
                        count_before = db_cursor.fetchone()[0]

                        db_cursor.execute("DELETE FROM topics")
                        db_connection.commit()
                        db_connection.close()

                        return f"已清空数据库，删除了 {count_before} 条记录", []
                    except Exception as e:
                        print(f"Error clearing database: {e}")
                        return f"清空数据库失败: {str(e)}", []

                # Function to update database selector
                def update_db_selector():
                    """Update the database selector dropdown"""
                    db_files = get_available_databases()
                    choices = [file_name for file_name, _ in db_files]
                    return gr.update(choices=choices, value=choices[0] if choices else None)

                # Set up event handlers
                refresh_btn.click(
                    fn=load_topics_from_database,
                    inputs=[db_file_selector],
                    outputs=topics_table,
                    show_progress="minimal",
                )

                # 启用主题数据库表格change事件处理器来实时增量更新数据库
                topics_table.change(
                    fn=on_topics_table_change,
                    inputs=[db_file_selector, topics_table],
                    outputs=topics_table,
                    show_progress="hidden",
                )

                # 添加主题数据库表格选择事件处理器
                topics_table.select(
                    fn=on_topics_table_select,
                    outputs=selected_rows_state_topics,
                )

                delete_selected_btn.click(
                    fn=delete_selected_rows,
                    inputs=[db_file_selector, selected_rows_state_topics, topics_table],
                    outputs=[status_output, topics_table],
                    show_progress="minimal",
                )

                clear_btn.click(
                    fn=clear_database,
                    inputs=[db_file_selector],
                    outputs=[status_output, topics_table],
                    show_progress="minimal",
                )

            # New Tab: Meeting Minutes Checker
            with gr.TabItem("会议记录检查"):
                # Layout with two columns
                with gr.Row():
                    # Left Column: Inputs
                    with gr.Column(scale=1):
                        # Checkpoints Input
                        checkpoints_input = gr.TextArea(
                            label="检查要点",
                            lines=8,
                            max_lines=8,
                            placeholder="请输入检查要点...",
                            container=False,
                        )

                        # Meeting Minutes Input
                        minutes_input = gr.TextArea(
                            label="会议记录",
                            lines=15,
                            max_lines=15,
                            placeholder="请输入会议记录内容...",
                            container=False,
                        )

                    # Right Column: Output
                    with gr.Column(scale=2):
                        # Check Result Output
                        result_output = gr.TextArea(
                            label="检查结果",
                            lines=25,
                            max_lines=25,
                            interactive=False,
                            show_copy_button=True,
                            container=False,
                        )

                # Add some spacing between sections
                gr.Markdown("&nbsp;")

                gr.Examples(
                    [
                        ["会议记录内容是否和Markdown文档内容有相关性，包括主题相关性、内容相关性、语言相关性、格式相关性。只输出检查结果，不输出检查过程。", "今天晚上我要去打羽毛球"]
                    ],
                    [checkpoints_input, minutes_input]
                )

                # Function to perform the check
                # 主要的检查函数
                async def check_minutes(pdf_path, checkpoints_input, minutes_input):
                    """检查会议记录是否包含所有检查要点，并使用OpenAI LLM逐页检查PDF对应的Markdown内容"""
                    if not minutes_input or not checkpoints_input:
                        yield "请输入会议记录和检查要点"
                        return

                    # Open the PDF document
                    doc = fitz.open(pdf_path)

                    # Get the markdown directory for the PDF
                    pdf_stem = Path(pdf_path).stem
                    md_dir = Path("output") / pdf_stem

                    if not md_dir.exists():
                        yield "# 错误\n\n未找到对应的Markdown文件，请先进行PDF转换"
                        return

                    # Initialize OpenAI model
                    try:
                        model_name = os.environ.get("OPENAI_MODEL_NAME", "gpt-4o")
                        api_key = os.environ.get("OPENAI_API_KEY", "sk-your-api-key")
                        base_url = os.environ.get("OPENAI_BASE_URL", "https://api.openai.com/v1")

                        openai_model = ChatOpenAI(
                            model=model_name,
                            temperature=0,
                            api_key=api_key,
                            base_url=base_url,
                            top_p=0.75,
                            seed=42,
                        )
                    except Exception as e:
                        yield f"# 错误\n\n初始化OpenAI模型时发生错误: {str(e)}"
                        return

                    # Get all markdown files for the PDF, sorted by page number
                    markdown_files = []
                    for page_num in range(1, len(doc) + 1):
                        markdown_path = get_markdown_path(pdf_path, page_num)
                        if markdown_path.exists():
                            markdown_files.append(markdown_path)

                    if not markdown_files:
                        yield "# 错误\n\n未找到任何Markdown文件，请先进行PDF转换"
                        return

                    # 分割检查点
                    checkpoint_list = [point.strip() for point in checkpoints_input.split('\n') if point.strip()]

                    # 为每个检查点和每页内容创建任务
                    tasks = []
                    for checkpoint in checkpoint_list:
                        for markdown_file in markdown_files:
                            with open(markdown_file, 'r', encoding='utf-8') as f:
                                markdown_content = f.read()
                            page_number = int(markdown_file.stem.split('_')[1])
                            task = stream_openai_check(markdown_content, checkpoint, minutes_input, openai_model)
                            tasks.append((task, checkpoint, page_number))

                    completed_result = ""

                    # 执行任务并逐个返回结果
                    for task, checkpoint, page_number in tasks:
                        result = ""
                        output_result = []
                        async for chunk in task:
                            result += chunk
                            formatted_result = []
                            # Stream the result as it's being generated
                            formatted_result.append(f"## Checkpoint: {checkpoint}")
                            formatted_result.append(f"### Page: {page_number}")
                            formatted_result.append(result)
                            formatted_result.append("-" * 80)
                            output_result = "\n\n".join(formatted_result)
                            yield completed_result + output_result
                        completed_result += output_result + "\n\n"

                # Create a button to trigger the check
                check_btn = gr.Button("🔍 开始检查", variant="primary")

                # Set up the button click event
                check_btn.click(
                    fn=check_minutes,
                    inputs=[pdf_input, checkpoints_input, minutes_input],
                    outputs=result_output,
                )

            # New Tab: Evaluation
            with gr.TabItem("评估"):
                # Layout with two columns
                with gr.Row():
                    # Left Column: Inputs
                    with gr.Column(scale=1):
                        # Radio Button
                        eval_method = gr.Radio(
                            choices=["embedding", "rerank", "chat"],
                            label="选择评估方法",
                            value="embedding"
                        )

                        # Text Input
                        text_input = gr.Textbox(
                            label="输入文本",
                            placeholder="请输入文本内容...",
                            container=False,
                        )

                        # Evaluation Button
                        eval_btn = gr.Button("📊 开始评估", variant="primary")

                    # Right Column: Output
                    with gr.Column(scale=2):
                        # Table Output
                        table_output = gr.DataFrame(
                            headers=["Content", "Score"],
                            datatype=["str", "number"],
                            label="评估结果",
                            interactive=False,
                        )

                # Function to perform the evaluation
                def evaluate(text, method):
                    # Placeholder for evaluation logic
                    # Replace with actual evaluation logic
                    test_data = [
                        "包装材料严重短缺未及时上报，导致今日三条产线停工待料，延误出口订单交期。必须追查仓储预警责任人，此类供应链断裂绝不可重复发生。",
                        "海外客户投诉产品外箱无防潮膜，海运途中受潮致百万损失。包装规范明确要求高湿度地区加防护，操作员未执行标准流程须严处。",
                        "玻璃瓶封装线连续三天破包率超15%，质检未叫停生产。现已积压三千件次品，包装组长监管失职必须追责到底。",
                        "贴标机参数被擅自更改，整批货500箱标签位置偏移遭超市拒收。包装技术员违规操作且隐瞒不报，必须顶格处罚。",
                        "环保包装袋厚度未达国标，被市场监管抽检公示罚款20万。采购主管为压成本降低质量标准，已构成重大失职行为。",
                        "危化品未用防静电包装，运输途中摩擦起火险酿大祸。安全培训记录显示该员工未参加本月安全考核，人资监管严重缺失。",
                        "礼盒组装线错配说明书版本，三千套春节礼盒全部返工。包装领班未执行首件确认流程，按制度扣除季度绩效奖金。",
                        "易碎品装箱未填充缓冲物，物流显示途中损毁率达47%。监控证实夜班包装员偷工减料，即刻解除劳动合同并追偿。",
                        "食品包装密封性检测仪故障三天未报修，致细菌超标遭客户索赔。设备管理员玩忽职守记大过处分，部门经理连带担责。",
                        "跨境电商货品内外箱唛头信息不符，整柜货物在海关滞留半月。包装信息核对员离岗超时无人顶替，必须整顿考勤纪律。",
                        "包装线混入劣质胶带，运输中箱体开裂丢失精密零件。经查为采购员收取供应商回扣，已移送司法机关处理。",
                        "药品铝塑包装日期喷码模糊，被药监局勒令下架召回。喷码操作员连续加班未轮休致工作失误，班组长排班不当记过。",
                        "出口货物木箱未做熏蒸处理，整批在目的港被销毁。包装检验员伪造检疫印章，予以开除并列入行业黑名单。",
                        "自动化包装机参数设置错误，导致三百箱产品真空漏气。技术员上岗三月未通过考核仍独立操作，培训主管停职审查。",
                        "冷藏品包装未放冰排，夏季运输途中全部变质。仓库温度记录表人为篡改，涉事三人全数辞退并追缴损失。",
                        "防伪标签贴覆不合格，市场出现大量仿冒品。包装质检抽检比例未达标准，质量总监降职处理。",
                        "重物包装箱抗压强度不足，堆码坍塌压毁下层货物。包装工程师计算承重数据失误，取消年度晋升资格。",
                        "医疗器械无菌包装袋封口温度偏低，微生物检测超标。工艺管理员未校准设备即投产，记重大质量事故。",
                        "电商促销包装过度使用填充物，物流成本超预算27%。包装设计主管未做成本核算，全部门通报批评。",
                        "农产品包装透气孔数量不足，运输途中发酵胀箱。新员工未经培训直接上岗，人资招聘流程存在重大漏洞。",
                        "出口欧洲产品包装含禁用的PVC材料，整批货物退运。包装研发经理忽略法规更新，处以损失金额10%罚款。",
                        "精密仪器防震包装未做跌落测试，到货开箱故障率31%。包装验证员敷衍签字，解除劳动合同并追溯赔偿。",
                        "危险品包装桶清洁不彻底混入杂质，引发客户生产线污染。清洗班组集体早退致工序遗漏，全组待岗培训一周。",
                        "礼品卡包装漏放磁条，两千份节日礼品作废。自动化包装机感应器被遮挡无人发现，当值技师严重失职。",
                        "可降解包装袋存储不当受潮粘连，产线停机清理六小时。仓库保管员未按规定湿度存放，扣发全年安全奖金。"
                    ]

                    openai_embeddings_base_url = os.getenv("OPENAI_EMBEDDINGS_BASE_URL")
                    print(f"{openai_embeddings_base_url=}")
                    if method == "embedding":
                        # Initialize LLM for embeddings
                        load_dotenv(find_dotenv())
                        llm = OpenAI(
                            api_key=os.getenv("OPENAI_API_KEY"),
                            base_url=openai_embeddings_base_url,
                            timeout=None,
                            max_retries=2
                        )

                        # Function to get embeddings for text
                        def get_embeddings(text, model="text-embedding-3-large"):
                            # Split text into chunks if needed
                            text_chunks = [text[i:i + 1024] for i in range(0, len(text), 1024)]
                            embeddings = []

                            for chunk in text_chunks:
                                response = llm.embeddings.create(input=chunk, model=model)
                                embeddings.extend([embedding.embedding for embedding in response.data])

                            return embeddings

                        # Get embedding for input text
                        input_embedding = get_embeddings(text, model=os.getenv("OPENAI_EMBED_MODEL_NAME"))

                        if not input_embedding:
                            return [["Error: Could not generate embedding", 0.0]]

                        # Calculate cosine similarity between two vectors
                        def cosine_similarity(vec1, vec2):
                            dot_product = sum(a * b for a, b in zip(vec1, vec2))
                            magnitude1 = sum(a * a for a in vec1) ** 0.5
                            magnitude2 = sum(b * b for b in vec2) ** 0.5
                            if magnitude1 == 0 or magnitude2 == 0:
                                return 0.0
                            return dot_product / (magnitude1 * magnitude2)

                        # Process each test data item
                        results = []
                        for item in test_data:
                            item_embedding = get_embeddings(item)
                            if item_embedding:
                                # Use the first embedding vector if multiple are returned
                                similarity = cosine_similarity(input_embedding[0], item_embedding[0])
                                results.append([item, similarity])

                        # Sort results by similarity score in descending order
                        results.sort(key=lambda x: x[1], reverse=True)

                        # Return all results for table_output
                        return results
                    elif method == "rerank":
                        # Load environment variables
                        load_dotenv(find_dotenv())
                        RERANKER_API_ENDPOINT = os.environ.get("RERANKER_API_ENDPOINT")
                        RERANKER_MODEL_NAME = os.environ.get("RERANKER_MODEL_NAME")

                        # Prepare the documents for reranking
                        results = []
                        for item in test_data:
                            # Call the rerank API
                            response = requests.post(RERANKER_API_ENDPOINT, json={
                                'model': RERANKER_MODEL_NAME,
                                'query': text,
                                'top_n': 1,
                                'documents': [item]
                            })

                            # Extract the relevance score from the response
                            try:
                                relevance_score = response.json().get('results', [{}])[0].get('relevance_score', 0.0)
                                results.append([item, relevance_score])
                            except (ValueError, KeyError):
                                # Default score if response is invalid
                                results.append([item, 0.0])

                        # Sort results by relevance score in descending order
                        results.sort(key=lambda x: x[1], reverse=True)
                        return results
                    elif method == "chat":
                        # Implement LLMReranker logic for chat method
                        class LLMReranker:
                            def __init__(self):
                                self.llm = self.set_up_llm()
                                self.system_prompt_rerank_single_block = """
                                You are a highly accurate ranking model. You need to output a JSON object with a single key 'relevance_score' that indicates how relevant the retrieved text block is to the query on a scale from 0 to 1.
                                """
                                self.system_prompt_rerank_multiple_blocks = """
                                You are a highly accurate ranking model. You need to output a JSON object with a single key 'block_rankings' that contains a list of objects, each with a 'relevance_score' key indicating how relevant each retrieved text block is to the query on a scale from 0 to 1.
                                """

                            def set_up_llm(self):
                                load_dotenv(find_dotenv())
                                llm = ChatOpenAI(api_key=os.getenv("OPENAI_API_KEY"), base_url=os.getenv("OPENAI_BASE_URL"))
                                return llm

                            def get_rank_for_single_block(self, query, retrieved_document):
                                user_prompt = f'\nHere is the query:\n"{query}"\n\nHere is the retrieved text block:\n"""\n{retrieved_document}\n"""\n'

                                messages = [
                                    SystemMessage(content=self.system_prompt_rerank_single_block),
                                    HumanMessage(content=user_prompt)
                                ]

                                response = self.llm.invoke(messages)
                                return {"relevance_score": response.content}

                            def get_rank_for_multiple_blocks(self, query, retrieved_documents):
                                formatted_blocks = "\n\n---\n\n".join([f'Block {i+1}:\n\n"""\n{text}\n"""' for i, text in enumerate(retrieved_documents)])
                                user_prompt = (
                                    f"Here is the query: \"{query}\"\n\n"
                                    "Here are the retrieved text blocks:\n"
                                    f"{formatted_blocks}\n\n"
                                    f"You should provide exactly {len(retrieved_documents)} rankings, in order."
                                )

                                messages = [
                                    SystemMessage(content=self.system_prompt_rerank_multiple_blocks),
                                    HumanMessage(content=user_prompt)
                                ]

                                response = self.llm.invoke(messages)
                                return {"block_rankings": response.content}

                            def rerank_documents(self, query: str, documents: list, documents_batch_size: int = 4, llm_weight: float = 0.7):
                                """
                                Rerank multiple documents using parallel processing with threading.
                                Combines vector similarity and LLM relevance scores using weighted average.
                                """
                                # Create batches of documents
                                doc_batches = [documents[i:i + documents_batch_size] for i in range(0, len(documents), documents_batch_size)]
                                vector_weight = 1 - llm_weight

                                if documents_batch_size == 1:
                                    def process_single_doc(doc):
                                        # Get ranking for single document
                                        ranking = self.get_rank_for_single_block(query, doc['text'])

                                        doc_with_score = doc.copy()
                                        doc_with_score["relevance_score"] = ranking["relevance_score"]
                                        # Calculate combined score - note that distance is inverted since lower is better
                                        doc_with_score["combined_score"] = round(
                                            llm_weight * ranking["relevance_score"] +
                                            vector_weight * doc['distance'],
                                            4
                                        )
                                        return doc_with_score

                                    # Process all documents in parallel using single-block method
                                    with ThreadPoolExecutor() as executor:
                                        all_results = list(executor.map(process_single_doc, documents))

                                else:
                                    def process_batch(batch):
                                        texts = [doc['text'] for doc in batch]
                                        rankings = self.get_rank_for_multiple_blocks(query, texts)
                                        results = []
                                        block_rankings = rankings.get('block_rankings', [])

                                        if len(block_rankings) < len(batch):
                                            print(f"\nWarning: Expected {len(batch)} rankings but got {len(block_rankings)}")
                                            for i in range(len(block_rankings), len(batch)):
                                                doc = batch[i]
                                                print(f"Missing ranking for document on page {doc.get('page', 'unknown')}:")
                                                print(f"Text preview: {doc['text'][:100]}...\n")

                                            for _ in range(len(batch) - len(block_rankings)):
                                                block_rankings.append({
                                                    "relevance_score": 0.0,
                                                    "reasoning": "Default ranking due to missing LLM response"
                                                })

                                        for doc, rank in zip(batch, block_rankings):
                                            doc_with_score = doc.copy()
                                            # Check if rank is a dictionary
                                            if isinstance(rank, dict) and "relevance_score" in rank:
                                                relevance_score = rank["relevance_score"]
                                            else:
                                                # Handle case where rank is a string or doesn't have relevance_score
                                                try:
                                                    # Try to parse as JSON if it's a string
                                                    rank_dict = json.loads(rank)
                                                    relevance_score = rank_dict.get("relevance_score", 0.0)
                                                except (json.JSONDecodeError, TypeError):
                                                    # Default to 0.0 if parsing fails
                                                    relevance_score = 0.0

                                            doc_with_score["relevance_score"] = relevance_score
                                            doc_with_score["combined_score"] = round(
                                                llm_weight * relevance_score +
                                                vector_weight * doc['distance'],
                                                4
                                            )
                                            results.append(doc_with_score)
                                        return results

                                    # Process batches in parallel using threads
                                    with ThreadPoolExecutor() as executor:
                                        batch_results = list(executor.map(process_batch, doc_batches))

                                    # Flatten results
                                    all_results = []
                                    for batch in batch_results:
                                        all_results.extend(batch)

                                # Sort results by combined score in descending order
                                all_results.sort(key=lambda x: x["combined_score"], reverse=True)
                                return all_results

                        # Create an instance of LLMReranker
                        reranker = LLMReranker()

                        # Prepare documents for reranking
                        documents = []
                        for i, doc in enumerate(test_data):
                            documents.append({
                                'text': doc,
                                'distance': 0.1 * (i + 1)  # Dummy distance values
                            })

                        # Rerank documents using LLMReranker
                        results = reranker.rerank_documents(text, documents)

                        # Format results for output
                        formatted_results = []
                        for result in results:
                            formatted_results.append([result['text'], result['combined_score']])

                        return formatted_results

                # Set up the button click event
                eval_btn.click(
                    fn=evaluate,
                    inputs=[text_input, eval_method],
                    outputs=table_output,
                )

                # Update database selector when tab is opened
                demo.load(
                    fn=update_db_selector,
                    outputs=db_file_selector,
                )

    return demo

def main():
    """主函数，用于启动应用程序"""
    app = create_app()

    app.queue()
    app.launch()

if __name__ == "__main__":
    main()